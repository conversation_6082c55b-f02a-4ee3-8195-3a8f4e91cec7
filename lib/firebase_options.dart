// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDICoutqyLHDlxZsb2ICsuIFAr4rx7n-Hs',
    appId: '1:743638960286:web:d55e45e7efe2cd471d538f',
    messagingSenderId: '743638960286',
    projectId: 'money-track-73be3',
    authDomain: 'money-track-73be3.firebaseapp.com',
    storageBucket: 'money-track-73be3.firebasestorage.app',
    measurementId: 'G-XBPNCNRHCT',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAk9XvCGjLpGNJ_9xkWNyZDyFv9y0aoaw0',
    appId: '1:743638960286:android:693bceea0e29136a1d538f',
    messagingSenderId: '743638960286',
    projectId: 'money-track-73be3',
    storageBucket: 'money-track-73be3.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCO6orWP0w2TjkdtsCYH_BWEZp_HbjOtT4',
    appId: '1:743638960286:ios:3c2d681742cf644b1d538f',
    messagingSenderId: '743638960286',
    projectId: 'money-track-73be3',
    storageBucket: 'money-track-73be3.firebasestorage.app',
    iosBundleId: 'com.example.moneyTrack',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCO6orWP0w2TjkdtsCYH_BWEZp_HbjOtT4',
    appId: '1:743638960286:ios:3c2d681742cf644b1d538f',
    messagingSenderId: '743638960286',
    projectId: 'money-track-73be3',
    storageBucket: 'money-track-73be3.firebasestorage.app',
    iosBundleId: 'com.example.moneyTrack',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDICoutqyLHDlxZsb2ICsuIFAr4rx7n-Hs',
    appId: '1:743638960286:web:d55e45e7efe2cd471d538f',
    messagingSenderId: '743638960286',
    projectId: 'money-track-73be3',
    authDomain: 'money-track-73be3.firebaseapp.com',
    storageBucket: 'money-track-73be3.firebasestorage.app',
    measurementId: 'G-XBPNCNRHCT',
  );
}